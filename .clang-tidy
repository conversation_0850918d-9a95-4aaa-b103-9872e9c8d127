# .clang-tidy configuration for Google C++ Style Guide
#
# This configuration enables checks recommended by Google and enforces
# Google's naming conventions.

Checks: '-*,
          google-*,
          bugprone-*,
          performance-*,
          portability-*,
          modernize-*,
          readability-*,
          -google-build-using-namespace,
          -google-runtime-references,
          -modernize-use-trailing-return-type'

WarningsAsErrors: ''

HeaderFilterRegex: '.*'

CheckOptions:
  # --------------------------------------------------------------------------
  # Naming Convention (readability-identifier-naming) for Google Style
  # See: https://google.github.io/styleguide/cppguide.html#Naming
  # --------------------------------------------------------------------------
  - key:             readability-identifier-naming.ClassCase
    value:           PascalCase
  - key:             readability-identifier-naming.StructCase
    value:           PascalCase
  - key:             readability-identifier-naming.EnumCase
    value:           PascalCase
  - key:             readability-identifier-naming.EnumConstantCase
    value:           PascalCase # Or UPPER_SNAKE_CASE
    # Google uses kPascalCase for constants, which is not a standard option.
    # We use PascalCase here as a close substitute, or you can use UPPER_SNAKE_CASE.
  - key:             readability-identifier-naming.FunctionCase
    value:           PascalCase
  - key:             readability-identifier-naming.GlobalConstantCase
    value:           k_PascalCase # This requires a newer clang-tidy version
    # If the above doesn't work, use a regex or just check manually.
  - key:             readability-identifier-naming.GlobalFunctionCase
    value:           PascalCase
  - key:             readability-identifier-naming.GlobalVariableCase
    value:           snake_case
  - key:             readability-identifier-naming.MemberCase
    value:           snake_case_with_suffix # Corresponds to `member_`
  - key:             readability-identifier-naming.ParameterCase
    value:           snake_case
  - key:             readability-identifier-naming.VariableCase
    value:           snake_case