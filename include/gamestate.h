//
// Created by <PERSON> on 25-7-25.
//

#ifndef GAMESTATE_H
#define GAMESTATE_H

#include <fstream>
#include <stdexcept>
#include <string>
#include <vector>
#include "bullet.h"
#include "tank.h"

class GameState {
 private:
  // Tank tankA, tankB;
  //  change to vector for future extensibility
  std::vector<Tank> tanks;      // a vector of all existing tanks
  std::vector<Bullet> bullets;  // a vector of all existing bullets
  int map_size;
  int turn;
  std::string mode;
  int initial_hp;            // the initial hp set by player
  std::string log_filename;  // for user designated filename of log
  std::ofstream log_file;    // the path where the log file will be written

 public:
  // Constructor:
  GameState(int init_hp, const std::string& m, const std::string& log_filename);
  // Destructor:
  ~GameState();
  // Copy Constructor:
  GameState(const GameState& other);
  // Assignment operator:
  GameState& operator=(const GameState& other);

  void initialize_tanks(int tankA_x, int tankA_y, Direction tankA_dir,
                        int tankB_x, int tankB_y, Direction tankB_dir);

  // Update game state each turn:
  void update_turn(MoveCommand moveA,
                   MoveCommand moveB);  // Adding input from 2 players;

  // Shrink the map size by 2 meters
  // void shrink_map();

  // Check if the game is finished and return the winner
  [[nodiscard]] int check_win() const;  // add 'const' declaration

  // ------------- OLD CODE ------------------
  // Check if the tanks are out of the map and deduct 1 hp if they are
  // void check_out_of_map();

  // Check whether the two tanks crushed and handle accordingly
  // void handle_crash();

  // Check whether tanks are hit by bullet and deduct hp accordingly
  // void handle_collision();
  // -----------------------------------------

  // check if game is over
  [[nodiscard]] bool is_game_over() const;

  // for logging
  void log_state();
  void log_end();

  // Getters
  [[nodiscard]] const Tank& get_tankA() const { return tanks[0]; }
  [[nodiscard]] const Tank& get_tankB() const { return tanks[1]; }
  [[nodiscard]] Tank& get_tankA() { return tanks[0]; }
  [[nodiscard]] Tank& get_tankB() {
    return tanks[1];
  }  // updated with vectors, and provide const and non-const getters
  [[nodiscard]] const std::vector<Bullet>& get_bullets() const {
    return bullets;
  }
  [[nodiscard]] std::vector<Bullet>& get_bullets() {
    return bullets;
  }  // for non-const acess.
  [[nodiscard]] int get_map_size() const { return map_size; }
  [[nodiscard]] int get_turn() const { return turn; }
  [[nodiscard]] const std::string& get_mode() const { return mode; }

  void set_map_size(int size) { map_size = size; }  // setter for map size
};
#endif