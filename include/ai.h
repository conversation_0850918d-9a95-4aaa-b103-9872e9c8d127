//
// Created by <PERSON> on 25-7-30.
//
#ifndef AI_H
#define AI_H

#include <cmath>
#include "gamestate.h"
#include "tank.h"

class AI {
 private:
  int id;
  double defensiveness;
  double aggression;
  int guardDistance;
  double intensionality;

 public:
  // Constructor:
  AI(int _id, double _defensiveness, int _guardDistance, double _aggression,
     double _intensionality);
  // Destructor:
  ~AI();

  // Evaluate the score of a situation.
  // The more favourable the situation is, the higher the score will be.
  double evaluateGameState(const GameState& state);

  // Calculate the score of a movement by using the Minimax algorithm.
  // This algorithm helps determine the optimal move for a tank by
  // exploring all possible moves and their outcomes in a game tree.
  // The algorithm assumes that the opponent will also play optimally.
  // Starting from the current game state, it recursively evaluates
  // all possible moves to a certain depth. At each level, it alternates
  // between maximizing (AI) and minimizing (opponent) players,
  // selecting the move with the highest or lowest score respectively.
  // The scores are propagated back up the tree to determine the
  // optimal move for AI.
  double minimax(const GameState& state, int depth, bool isMaximizingPlayer,
                 MoveCommand move);

  // Using the algorithm above to decide next movement.
  MoveCommand decide_move(const GameState& state);
};

#endif  // AI_H