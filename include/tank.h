//
// Created by <PERSON><PERSON> on 25-7-25.
//

#ifndef TANK_H
#define TANK_H

#include <vector>
// forward declaration
class Bullet;
enum class Direction { Up, Down, Left, Right };
enum class MoveCommand { Forward, Left, Right };

class Tank {
 private:
  int x, y;             // coordinate, int, in meter
  Direction direction;  // current direction
  int hp;
  int shoot_timer;  // to count when to shout. 0 means can shout, resets to 3.
  int id;           // 0 for A, 1 for B

 public:
  // Constructor:
  Tank(int tank_id, int init_x, int init_y, int init_hp, Direction init_dir);
  // Destructor:
  ~Tank();
  // Movement
  void move(MoveCommand cmd);

  // Shooting: 2 meters at front, same dir
  void shoot(std::vector<Bullet>& bullets);

  // Update shoot timer each turn
  void update_timer();

  [[nodiscard]] bool is_hit(const Bullet& bullet) const;

  void deduct_hp(int amount);

  [[nodiscard]] bool is_alive() const { return hp > 0; }

  // getters
  [[nodiscard]] int get_x() const { return x; }
  [[nodiscard]] int get_y() const { return y; }
  [[nodiscard]] Direction get_direction() const { return direction; }
  [[nodiscard]] int get_hp() const { return hp; }
  [[nodiscard]] int get_id() const { return id; }
  [[nodiscard]] int get_shoot_timer() const { return shoot_timer; }
};
#endif  // TANK_H
