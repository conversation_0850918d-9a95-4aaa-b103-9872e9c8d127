//
// Created by <PERSON><PERSON> on 25-7-27.
//

#ifndef GAMELOGIC_H
#define GAMELOGIC_H
#include "gamestate.h"
// some simple game logic functions
class GameLogic {
 public:
  static void handle_collision(GameState& state); // Handles the collision between bullets and tanks
  static void update_map_size(GameState& state);  // Updates the map size each 16 turns
  static void apply_boundary_damage(GameState& state);  // Apply damage to tanks that are out of the map bounds
  static void remove_out_of_bounds_bullets(GameState& state); // Remove bullets that are out of the map bounds
  static void handle_tank_collision(GameState& state);  // Handles the collision between tanks
};

#endif  // GAMELOGIC_H
