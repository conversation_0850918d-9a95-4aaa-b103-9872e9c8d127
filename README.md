# VG101 P2 Team 05: Tank War

## Project Overview
A C++ implementation of Tank War game for VG101 Project 2.

## Team Members
- **Member 1**: <PERSON><PERSON>
- **Member 2**: Yu Lu

## Project Structure
### Layer1: Data Layer
- tank.h, tank.cpp
  - Properties: hp, position(x,y), direction, id (0 for A, 1 for B), shoot timer (0 means to shoot, resets to 3);
  - Methods: 
    - 6 getters function for the properties, e.g., ``tank.get_id()`` to know which tank it is;
    - ``tank.move()`` to move the tank;
    - ``tank.shoot()`` to shoot a bullet;
    - ``tank.is_hit()`` to check if hit by another's bullet;
    - ``tank.update_timer()`` for updating the shooting timerl
    - ``tank.deduct_hp()`` to deduct hp when hit;
    - ``tank.is_alive()`` to check if the tank is still alive;
- bullet.h, bullet.cpp
  - Properties: position(x,y), direction, owner (0 for A, 1 for B);
  - Methods:
    - 4 getters function for the properties, e.g., ``bullet.get_owner()`` to know which bullet it is;
    - ``bullet.move()`` to move the bullet, 2 meters each turn;
    - ``bullet.is_out_of_boundary()`` to check if hit by another's tank;

### Layer2: Game Logic Layer
- gamestate.h, gamestate.cpp
  - Properties:
    - `tanks`: A vector containing all the tanks in the game.
    - `bullets`: A vector containing all the bullets currently in the game.
    - `map_size`: The current size of the game map.
    - `turn`: The current turn number.
    - `mode`: The mode of the game.
    - `initial_hp`: The initial hit points set by the player.
    - `log_filename`: The filename designated by the user for the log.
    - `log_file`: The file stream used to write the log.
  - Methods:
    - 9 getters function for the properties, e.g., ``get_map_size()`` to know current map size;
    - `initialize_tanks(...)`: Initializes the tanks with specified positions and directions.
    - `update_turn(...)`: Updates the game state for each turn based on the move commands from the two players.
    - `check_win()`: Checks if the game is finished and returns the winner.
    - `is_game_over()`: Checks if the game is over based on the result of `check_win()`.
    - `log_state()`: Logs the current state of the game to the log file.
    - `log_end()`: Logs the end result of the game to the log file.

- gamelogic.h, gamelogic.cpp
  - Methods:
    - `handle_collision(...)`: Handles the collision between bullets and tanks.
    - `update_map_size(...)`: Updates the map size based on the current turn.
    - `apply_boundary_damage(...)`: Applies damage to tanks that are out of the map bounds.
    - `remove_out_of_bounds_bullets(...)`: Removes bullets that are out of the map bounds.
    - `handle_tank_collision(...)`: Handles the collision between two tanks.

### Layer4: AI Extension Layer
- ai.h, ai.cpp
  - Properties: id, defensiveness, aggression, guardDistance, intensionality;
  - Methods: 
    - `evaluateGameState(GameState)`: Evaluate the score of a situation based on various standards.
    - `minimax(...)`: Calculate the score of a movement by using the Minimax algorithm.
    - `decide_move(GameState)`: Decide the best next move for the AI tank. 

## Development Environment Setup

### Prerequisites
```bash
# Install required tools
sudo apt-get update
sudo apt-get install build-essential cmake clang-format clang-tidy git

# Or on macOS
brew install cmake clang-format
```

### Initial Setup
1. Clone the repository
2. The git hooks are already configured and will run automatically
3. Make sure clang-format and clang-tidy are installed for code quality checks

## Development Rules & Guidelines

### Git Workflow Rules

#### Commit Message Format (MANDATORY)
We use **Conventional Commits** format. The commit-msg hook will enforce this:

```
type(scope): description

Examples:
feat: add tank movement system
fix(collision): resolve boundary detection bug
docs: update build instructions
test: add unit tests for weapon system
refactor: simplify game loop logic
```

**Valid commit types:**
- `feat` - New features
- `fix` - Bug fixes
- `docs` - Documentation changes
- `style` - Code style changes (formatting, etc.)
- `refactor` - Code refactoring
- `test` - Adding or updating tests
- `chore` - Build process, dependencies, etc.
- `perf` - Performance improvements

#### Branch Strategy
- `master` - Production-ready code (protected)
- `dev` - Integration branch
- `feature/feature-name` - New features
- `fix/bug-description` - Bug fixes
- `hotfix/urgent-fix` - Critical fixes
- After realizing new features or fixes, merge them into `dev` branch via pull requests.

#### Git Rules
1. **Never push directly to master** - Always use pull requests
2. **Always pull before starting work** - `git pull origin master` / `git pull origin dev`
3. **Create feature branches** for new work
4. **Write descriptive commit messages** - Follow the format above
5. **Commit frequently** with small, logical changes
6. **Review each other's code** before merging

### Code Style Rules (ENFORCED BY HOOKS)

#### Automatic Formatting
- **clang-format** runs automatically on every commit
- Based on **Google C++ Style Guide**
- 80-character line limit
- 2-space indentation, no tabs
- Your code will be automatically formatted - no manual formatting needed!

#### Naming Conventions (Enforced by clang-tidy)
```cpp
// Classes, Structs, Enums - PascalCase
class GameEngine { };
struct PlayerData { };
enum GameState { };

// Functions - PascalCase
void StartGame();
int CalculateScore();

// Variables, Parameters - snake_case
int player_score;
std::string game_mode;

// Member variables - snake_case with trailing underscore
class Tank {
private:
    int health_;
    float position_x_;
};

// Constants - kPascalCase
const int kMaxPlayers = 4;
const float kGravity = 9.8f;
```

#### Code Quality Rules
1. **Always use meaningful names** - no single letters except for loops
2. **Add comments for complex logic** - explain WHY, not WHAT
3. **Keep functions small** - one responsibility per function
4. **Use const correctness** - const wherever possible
5. **Avoid magic numbers** - use named constants
6. **Handle errors properly** - don't ignore return values

### Testing Rules

#### Test Requirements
1. **Write tests for new features** - Test-driven development preferred
2. **Test edge cases** - boundary conditions, invalid inputs
3. **All tests must pass** before merging
4. **Maintain test coverage** - aim for >80% coverage

#### Test Naming
```cpp
// Test function names should be descriptive
TEST(TankTest, ShouldMoveCorrectlyWithValidInput) { }
TEST(CollisionTest, ShouldDetectBoundaryCollision) { }
```

### Build Rules

#### Before Every Commit
The pre-commit hook automatically:
1. **Formats your code** with clang-format
2. **Runs static analysis** with clang-tidy
3. **Re-stages formatted files**

#### Manual Build Commands
```bash
# Create build directory
mkdir build && cd build

# Configure with CMake
cmake ..

# Build the project
make -j$(nproc)

# Run tests
make test
# or
ctest --verbose
```

#### Build Requirements
1. **Code must compile** without warnings
2. **All tests must pass**
3. **No clang-tidy errors** for new code
4. **Follow memory management** - no leaks

### Workflow Example

```bash
# 1. Start new feature
git checkout master
git pull origin master
git checkout -b feature/tank-movement

# 2. Make changes, commit frequently
# (hooks will automatically format and validate)
git add .
git commit -m "feat: add basic tank movement"

# 3. Push and create PR
git push origin feature/tank-movement
# Create pull request on GitHub

# 4. After review and approval, merge
# Delete feature branch after merge
```

### Important Warnings

#### DO NOT:
- Push directly to main branch
- Commit without descriptive messages
- Ignore compiler warnings
- Skip code reviews
- Commit broken code
- Use `git commit --no-verify` (bypasses hooks)

#### ALWAYS:
- Test your changes before committing
- Write meaningful commit messages
- Review teammate's code thoroughly
- Keep commits small and focused
- Update documentation when needed
- Run the full build before pushing

## Troubleshooting

### Git Hooks Issues
```bash
# If hooks aren't running, check permissions
ls -la .git/hooks/
chmod +x .git/hooks/pre-commit .git/hooks/commit-msg

# If clang-format is missing
sudo apt-get install clang-format

# If you need to bypass hooks (emergency only!)
git commit --no-verify -m "emergency: fix critical bug"
```

### Build Issues
```bash
# Clean build
rm -rf build/
mkdir build && cd build
cmake .. && make clean && make

# Check for missing dependencies
ldd your_executable
```

### Code Style Issues
```bash
# Manually format a file
clang-format -i src/your_file.cpp

# Check what clang-tidy would suggest
clang-tidy src/your_file.cpp -- -std=c++17
```

## Resources

- [Google C++ Style Guide](https://google.github.io/styleguide/cppguide.html)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Git Flow Workflow](https://www.atlassian.com/git/tutorials/comparing-workflows/gitflow-workflow)
- [CMake Tutorial](https://cmake.org/cmake/help/latest/guide/tutorial/)

## Getting Help

1. **Check this README first**
2. **Ask your teammate** - pair programming encouraged!
3. **Check git hooks output** - they provide helpful error messages
4. **Review course materials** and documentation

---

**Remember**: The git hooks are there to help maintain code quality and consistency. Don't fight them - embrace them!
```
