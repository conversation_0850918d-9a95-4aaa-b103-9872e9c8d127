cmake_minimum_required(VERSION 3.16)
project(TankWar VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Compiler flags
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
# Note: -Werror disabled to allow compilation with warnings
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# Include directories
include_directories(include)

# Source files
file(GLOB_RECURSE SOURCES "src/*.cpp")
file(GLOB_RECURSE HEADERS "include/*.h")

# Create executable
add_executable(tankwar ${SOURCES} ${HEADERS}
        src/tank.cpp
        include/bullet.h
        src/bullet.cpp
        include/gamelogic.h
        src/gamelogic.cpp)

# Enable testing
enable_testing()

# Find and link test framework (assuming Google Test)
find_package(GTest QUIET)
if(GTest_FOUND)
    file(GLOB_RECURSE TEST_SOURCES "tests/*.cpp")
    add_executable(tankwar_tests ${TEST_SOURCES} ${SOURCES})
    target_link_libraries(tankwar_tests GTest::gtest GTest::gtest_main)
    
    # Remove main.cpp from test build if it exists
    list(FILTER SOURCES EXCLUDE REGEX ".*main\\.cpp$")
    
    add_test(NAME TankWarTests COMMAND tankwar_tests)
endif()

# Custom target for formatting
find_program(CLANG_FORMAT clang-format)
if(CLANG_FORMAT)
    add_custom_target(format
        COMMAND ${CLANG_FORMAT} -i ${SOURCES} ${HEADERS}
        COMMENT "Formatting source code with clang-format"
    )
endif()