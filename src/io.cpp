#include "../include/io.h"
#include <getopt.h>
#include <iomanip>
#include <iostream>
#include <sstream>

void IO::parse_command_line(int argc, char* argv[], int& hp, std::string& mode,
                            std::string& log_file) {
  static struct option long_options[] = {
      {"help", no_argument, 0, 'h'},
      {"log-file", required_argument, 0, 'l'},
      {"mode", required_argument, 0, 'm'},
      {"initial-life", required_argument, 0, 'i'},
      {0, 0, 0, 0}};

  int opt;
  while ((opt = getopt_long(argc, argv, "hl:m:i:", long_options, nullptr)) !=
         -1) {
    switch (opt) {
      case 'h':
        print_help();
        exit(0);
      case 'l':
        log_file = optarg;
        break;
      case 'm':
        mode = optarg;
        break;
      case 'i':
        hp = std::stoi(optarg);
        break;
    }
  }
}

MoveCommand IO::get_user_input(const std::string& player) {
  print_message(player + "'s turn. Enter move (forward/left/right or W/A/D): ");
  std::string input;
  std::cin >> input;

  if (input == "forward" || input == "W") return MoveCommand::Forward;
  if (input == "left" || input == "A") return MoveCommand::Left;
  if (input == "right" || input == "D") return MoveCommand::Right;

  // If we reach here, input was invalid
  print_message("Invalid input! Please try again.");
  return IO::get_user_input(player);
}

void IO::print_map(const GameState& state) {
  std::cout << std::endl;
  std::cout << "Turn: " << state.get_turn() << " Map: " << state.get_map_size()
            << "x" << state.get_map_size() << std::endl;

  // Get direction information for display
  Direction dirA = state.get_tankA().get_direction();
  Direction dirB = state.get_tankB().get_direction();
  std::string directionA = get_direction_arrow(dirA);
  std::string directionB = get_direction_arrow(dirB);

  for (int row = 25; row >= -4; --row) {
    std::cout << "|";
    for (int col = -4; col <= 25; ++col) {
      std::string display = " ";

      // Collect all bullets at this position
      std::vector<const Bullet*> bullets_here;
      for (const auto& bullet : state.get_bullets()) {
        if (col == bullet.get_x() && row == bullet.get_y()) {
          bullets_here.push_back(&bullet);
        }
      }

      // Check for tanks and show them with direction (tanks have priority)
      if (col == state.get_tankA().get_x() &&
          row == state.get_tankA().get_y()) {
        display = "A";
        display += get_direction_symbol(state.get_tankA().get_direction());
      } else if (col == state.get_tankB().get_x() &&
                 row == state.get_tankB().get_y()) {
        display = "B";
        display += get_direction_symbol(state.get_tankB().get_direction());
      } else if (!bullets_here.empty()) {
        // Smart bullet display based on number and ownership
        display = get_bullet_display(bullets_here);
      } else if (row < 11 - state.get_map_size() / 2 ||
                 row > 10 + state.get_map_size() / 2 ||
                 col < 11 - state.get_map_size() / 2 ||
                 col > 10 + state.get_map_size() / 2) {
        display = "-";
      }

      // Print the display string, but pad to ensure consistent spacing
      if (display.length() == 2) {
        std::cout << display[0] << display[1];
      } else {
        std::cout << display[0] << " ";
      }
      std::cout << "|";
    }
    std::cout << std::endl;
  }

  std::cout << std::endl;

  // Enhanced direction and status display with proper alignment
  const int box_width = 60;
  const int half_width = 28;

  std::cout << "+" << std::string(box_width, '-') << "+" << std::endl;

  // Direction row
  std::string tankA_dir = "Tank A " + directionA;
  std::string tankB_dir = "Tank B " + directionB;
  std::cout << "| " << std::left << std::setw(half_width) << tankA_dir << " | "
            << std::left << std::setw(half_width) << tankB_dir << " |"
            << std::endl;

  // HP row
  std::ostringstream hpA_stream, hpB_stream;
  hpA_stream << "HP: " << state.get_tankA().get_hp();
  hpB_stream << "HP: " << state.get_tankB().get_hp();
  std::cout << "| " << std::left << std::setw(half_width) << hpA_stream.str()
            << " | " << std::left << std::setw(half_width) << hpB_stream.str()
            << " |" << std::endl;

  // Position row
  std::ostringstream posA_stream, posB_stream;
  posA_stream << "Pos: (" << state.get_tankA().get_x() << ","
              << state.get_tankA().get_y() << ")";
  posB_stream << "Pos: (" << state.get_tankB().get_x() << ","
              << state.get_tankB().get_y() << ")";
  std::cout << "| " << std::left << std::setw(half_width) << posA_stream.str()
            << " | " << std::left << std::setw(half_width) << posB_stream.str()
            << " |" << std::endl;

  std::cout << "+" << std::string(box_width, '-') << "+" << std::endl;
}

void IO::print_help() {
  std::cout << "Usage: ./tankwar [options]\n"
            << "Options:\n"
            << "  -h, --help            Print this help message\n"
            << "  -l, --log-file <file> Log file (default: tankwar.log)\n"
            << "  -m, --mode <mode>     Mode: PVP/PVE/DEMO (default: PVP)\n"
            << "  -i, --initial-life <num> Initial HP (default: 5)\n";
}

void IO::print_message(const std::string& msg) {
  std::cout << msg << std::endl;
}

void IO::print_end(const GameState& state) {
  std::cout << std::endl;
  if (state.check_win() == 0)
    std::cout << "Game ends! Tank A is the winner." << std::endl;
  else if (state.check_win() == 1)
    std::cout << "Game ends! Tank B is the winner." << std::endl;
  else if (state.check_win() == 2)
    std::cout << "Game ends! It's a draw competition." << std::endl;
}

// Helper function to get direction symbol for map display
char IO::get_direction_symbol(Direction dir) {
  switch (dir) {
    case Direction::Up:
      return '^';
    case Direction::Down:
      return 'v';
    case Direction::Left:
      return '<';
    case Direction::Right:
      return '>';
    default:
      return '?';
  }
}

// Helper function to get direction arrow for status display
std::string IO::get_direction_arrow(Direction dir) {
  switch (dir) {
    case Direction::Up:
      return "↑ UP   ";
    case Direction::Down:
      return "↓ DOWN ";
    case Direction::Left:
      return "← LEFT ";
    case Direction::Right:
      return "→ RIGHT";
    default:
      return "? UNKNOWN";
  }
}

// Helper function to get tank symbol with direction
std::string IO::get_tank_symbol(char tank_id, Direction dir) {
  return std::string(1, tank_id) + get_direction_symbol(dir);
}

// Smart bullet display function - shows direction and handles overlapping
// bullets
std::string IO::get_bullet_display(const std::vector<const Bullet*>& bullets) {
  if (bullets.empty()) return " ";

  if (bullets.size() == 1) {
    // Single bullet - just show the direction symbol, clean and simple
    const Bullet* bullet = bullets[0];
    return std::string(1, get_direction_symbol(bullet->get_direction())) + " ";
  } else if (bullets.size() == 2) {
    // Two bullets - check if they're going in the same direction
    Direction dir1 = bullets[0]->get_direction();
    Direction dir2 = bullets[1]->get_direction();

    if (dir1 == dir2) {
      // Same direction - show direction with count
      return std::string(1, get_direction_symbol(dir1)) + "2";
    } else {
      // Different directions - show crossing
      return "X ";
    }
  } else {
    // 3+ bullets - show count
    return std::to_string(bullets.size()) + " ";
  }
}