//
// Created by <PERSON><PERSON> on 25-7-27.
//
#include "../include/gamelogic.h"
#include <algorithm>

void GameLogic::handle_collision(GameState& state) {
  auto& bullets = state.get_bullets();
  auto& tankA = state.get_tankA();
  auto& tankB = state.get_tankB();

  // check collisions for bullet on tank
  for (auto it = bullets.begin(); it != bullets.end();) {
    bool hit = false;
    if (tankA.is_hit(*it)) {
      tankA.deduct_hp(2);
      hit = true;
    }

    if (tankB.is_hit(*it)) {
      tankB.deduct_hp(2);
      hit = true;
    }
    if (hit) {
      it = bullets.erase(it);  // remove bullet if it hits a tank
    } else {
      ++it;  // move to the next bullet
    }
  }
}

void GameLogic::update_map_size(GameState& state) {
  // EDIT: move the judgement of turn%16 to this function
  if (state.get_turn() % 16 == 0 && state.get_turn() > 0) {
    int current_size = state.get_map_size();
    int new_size = std::max(0, current_size - 2);
    state.set_map_size(new_size);
  }
}

void GameLogic::apply_boundary_damage(GameState& state) {
  auto& tankA = state.get_tankA();
  auto& tankB = state.get_tankB();
  int map_size = state.get_map_size();

  // SETTINNG: the map will shrink toward the center instead of one corner
  // check bound for A and deduct q hp if it's out of current map
  if (tankA.get_x() < 11 - map_size / 2 || tankA.get_x() > 10 + map_size / 2 ||
      tankA.get_y() < 11 - map_size / 2 || tankA.get_y() > 10 + map_size / 2) {
    tankA.deduct_hp(1);
  }
  // for B
  if (tankB.get_x() < 11 - map_size / 2 || tankB.get_x() > 10 + map_size / 2 ||
      tankB.get_y() < 11 - map_size / 2 || tankB.get_y() > 10 + map_size / 2) {
    tankB.deduct_hp(1);
  }
}

void GameLogic::remove_out_of_bounds_bullets(GameState& state) {
  auto& bullets = state.get_bullets();
  int map_size = state.get_map_size();

  // removal
  bullets.erase(std::remove_if(bullets.begin(), bullets.end(),
                               [map_size](const Bullet& bullet) {
                                 return bullet.is_out_of_bounds();
                               }),
                bullets.end());
}

void GameLogic::handle_tank_collision(GameState& state) {
  auto& tankA = state.get_tankA();
  auto& tankB = state.get_tankB();
  bool crash = false;

  // Check if tanks crash
  if (tankA.get_x() == tankB.get_x() && tankA.get_y() == tankB.get_y())
    crash = true;
  // Avoid the tanks from passing through each other
  if ((tankA.get_direction() == Direction::Up &&
       tankB.get_direction() == Direction::Down &&
       tankA.get_x() == tankB.get_x() && tankA.get_y() == tankB.get_y() + 1) ||
      (tankA.get_direction() == Direction::Down &&
       tankB.get_direction() == Direction::Up &&
       tankA.get_x() == tankB.get_x() && tankA.get_y() == tankB.get_y() - 1) ||
      (tankA.get_direction() == Direction::Right &&
       tankB.get_direction() == Direction::Left &&
       tankA.get_x() == tankB.get_x() + 1 && tankA.get_y() == tankB.get_y()) ||
      (tankA.get_direction() == Direction::Left &&
       tankB.get_direction() == Direction::Right &&
       tankA.get_x() == tankB.get_x() - 1 && tankA.get_y() == tankB.get_y()))
    crash = true;

  if (crash == true) {
    if (tankA.get_hp() > tankB.get_hp()) {
      // A wins
      tankB.deduct_hp(tankB.get_hp());
    } else if (tankA.get_hp() < tankB.get_hp()) {
      // B wins
      tankA.deduct_hp(tankA.get_hp());
    } else {
      // draw
      tankA.deduct_hp(tankA.get_hp());
      tankB.deduct_hp(tankB.get_hp());
    }
  }
}
