#include <iostream>
#include "../include/ai.h"
#include "../include/gamestate.h"
#include "../include/io.h"

int main(int argc, char* argv[]) {
  int hp = 5;
  std::string mode = "PVP", log_file = "tankwar.log";
  IO::parse_command_line(argc, argv, hp, mode, log_file);
  GameState state(hp, mode, log_file);
  const Direction directions[4] = {Direction::Up, Direction::Down,
                                   Direction::Left, Direction::Right};

  IO::print_message(
      "Enter Tank A position (x y dir: 0=Up 1=Down 2=Left 3=Right): ");
  int ax, ay, adir;
  std::cin >> ax >> ay >> adir;
  Direction a_dir = static_cast<Direction>(adir);
  // Direction a_dir = directions[adir];
  IO::print_message(
      "Enter Tank B position (x y dir: 0=Up 1=Down 2=Left 3=Right): ");
  int bx, by, bdir;
  std::cin >> bx >> by >> bdir;
  Direction b_dir = static_cast<Direction>(bdir);
  // Direction b_dir = directions[bdir];
  state.initialize_tanks(ax, ay, a_dir, bx, by, b_dir);

  while (state.is_game_over() == false) {
    MoveCommand moveA, moveB;
    AI aiA(0, 0.5, 8, 0.1, 0.1);
    AI aiB(1, 0.2, 4, 0.5, 0.5);
    if (mode == "PVP") {
      moveA = IO::get_user_input("Player 1 (Tank A)");
      moveB = IO::get_user_input("Player 2 (Tank B)");
    }

    else if (mode == "PVE") {
      moveA = IO::get_user_input("Player 1 (Tank A)");
      moveB = aiB.decide_move(state);
    }

    else if (mode == "EVE") {
      moveA = aiA.decide_move(state);
      moveB = aiB.decide_move(state);
    }

    state.update_turn(moveA, moveB);
    IO::print_map(state);
    state.log_state();
  }
  state.log_end();
  IO::print_end(state);
  return 0;
}