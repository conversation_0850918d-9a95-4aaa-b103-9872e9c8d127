//
// Created by <PERSON><PERSON> on 25-7-26.
//

#include "../include/tank.h"
#include <algorithm>
#include <vector>

#include "../include/bullet.h"

Tank::Tank(int tank_id, int init_x, int init_y, int init_hp, Direction init_dir)
    : x(init_x),
      y(init_y),
      direction(init_dir),
      hp(init_hp),
      shoot_timer(0),
      id(tank_id) {}

Tank::~Tank() = default;

void Tank::move(MoveCommand cmd) {
  // Each command moves 1 meter
  // IMPORTANT SETTING: since it is not clear in the p2.pdf rule, we decided
  // "turn left" means from the driver's POV, i.e., if current direction = left,
  // ... turn left would alter the direction to be left's left, that is ,
  // backward. After adjusting the direction, it would move in the corresponding
  // direction one meter
  if (cmd == MoveCommand::Left) {
    // adjust direction first
    switch (direction) {
      case Direction::Up:
        direction = Direction::Left;
        break;
      case Direction::Left:
        direction = Direction::Down;
        break;
      case Direction::Down:
        direction = Direction::Right;
        break;
      case Direction::Right:
        direction = Direction::Up;
        break;
    }
  } else if (cmd == MoveCommand::Right) {
    // similar as above
    switch (direction) {
      case Direction::Up:
        direction = Direction::Right;
        break;
      case Direction::Right:
        direction = Direction::Down;
        break;
      case Direction::Down:
        direction = Direction::Left;
        break;
      case Direction::Left:
        direction = Direction::Up;
        break;
    }
  }

  // After adjusting the direction, move 1 meter in the current direction
  switch (direction) {
    case Direction::Up:
      y += 1;
      break;
    case Direction::Down:
      y -= 1;
      break;
    case Direction::Right:
      x += 1;
      break;
    case Direction::Left:
      x -= 1;
      break;
  }
}

void Tank::shoot(std::vector<Bullet>& bullets) {
  if (shoot_timer == 0) {
    // shoot a bullet in the current direction
    int bullet_x = x;
    int bullet_y = y;

    switch (direction) {
      case Direction::Up:
        bullet_y += 2;
        break;
      case Direction::Down:
        bullet_y -= 2;
        break;
      case Direction::Right:
        bullet_x += 2;
        break;
      case Direction::Left:
        bullet_x -= 2;
        break;
    }

    bullets.emplace_back(bullet_x, bullet_y, direction, id);
    shoot_timer = 3;  // reset shoot timer
  }
}

void Tank::update_timer() {
  if (shoot_timer > 0) {
    shoot_timer--;
  }
  // only deal with timer > 0; since otherwise it should be dealt with shoot()
  // function
}

bool Tank::is_hit(const Bullet& bullet) const {
  // We decide that the bullet can only hurt the enemy, not yourself
  // and a tank is hit if it's in a bullet's flight trajectory
  Direction direction = bullet.get_direction();
  // ensure that when a tank shoots out a bullet, the position 1 meter ahead of
  // the tank can also be hit:
  if (bullet.get_turns() == 1) {
    switch (direction) {
      case Direction::Up:
        return (((x == bullet.get_x() && y == bullet.get_y()) ||
                 (x == bullet.get_x() && y == bullet.get_y() - 1) ||
                 (x == bullet.get_x() && y == bullet.get_y() - 2) ||
                 (x == bullet.get_x() && y == bullet.get_y() - 3)) &&
                bullet.get_owner() != id);
        break;
      case Direction::Down:
        return (((x == bullet.get_x() && y == bullet.get_y()) ||
                 (x == bullet.get_x() && y == bullet.get_y() + 1) ||
                 (x == bullet.get_x() && y == bullet.get_y() + 2) ||
                 (x == bullet.get_x() && y == bullet.get_y() + 3)) &&
                bullet.get_owner() != id);
        break;
      case Direction::Right:
        return (((x == bullet.get_x() && y == bullet.get_y()) ||
                 (x == bullet.get_x() - 1 && y == bullet.get_y()) ||
                 (x == bullet.get_x() - 2 && y == bullet.get_y()) ||
                 (x == bullet.get_x() - 3 && y == bullet.get_y())) &&
                bullet.get_owner() != id);
        break;
      case Direction::Left:
        return (((x == bullet.get_x() && y == bullet.get_y()) ||
                 (x == bullet.get_x() + 1 && y == bullet.get_y()) ||
                 (x == bullet.get_x() + 2 && y == bullet.get_y()) ||
                 (x == bullet.get_x() + 3 && y == bullet.get_y())) &&
                bullet.get_owner() != id);
        break;
    }
  }
  switch (direction) {
    case Direction::Up:
      return (((x == bullet.get_x() && y == bullet.get_y()) ||
               (x == bullet.get_x() && y == bullet.get_y() - 1) ||
               (x == bullet.get_x() && y == bullet.get_y() - 2)) &&
              bullet.get_owner() != id);
      break;
    case Direction::Down:
      return (((x == bullet.get_x() && y == bullet.get_y()) ||
               (x == bullet.get_x() && y == bullet.get_y() + 1) ||
               (x == bullet.get_x() && y == bullet.get_y() + 2)) &&
              bullet.get_owner() != id);
      break;
    case Direction::Right:
      return (((x == bullet.get_x() && y == bullet.get_y()) ||
               (x == bullet.get_x() - 1 && y == bullet.get_y()) ||
               (x == bullet.get_x() - 2 && y == bullet.get_y())) &&
              bullet.get_owner() != id);
      break;
    case Direction::Left:
      return (((x == bullet.get_x() && y == bullet.get_y()) ||
               (x == bullet.get_x() + 1 && y == bullet.get_y()) ||
               (x == bullet.get_x() + 2 && y == bullet.get_y())) &&
              bullet.get_owner() != id);
      break;
  }
  return false;
}

void Tank::deduct_hp(int amount) {
  hp -= amount;
  hp = std::max(hp, 0);
}
