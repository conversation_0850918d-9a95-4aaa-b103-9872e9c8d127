//
// Created by <PERSON> on 25-7-25.
//

#include "../include/gamestate.h"
#include "../include/gamelogic.h"

// Constructor:
GameState::GameState(int init_hp, const std::string& m,
                     const std::string& log_filename)
    : map_size(20),
      turn(0),
      mode(m),
      initial_hp(init_hp),
      log_filename(log_filename) {
  log_file.open(this->log_filename);
  if (!log_file.is_open()) {
    throw std::runtime_error("Failed to open log file: " + this->log_filename);
  }

  tanks.reserve(2);
}

// Destructor:
GameState::~GameState() {
  if (log_file.is_open()) {
    log_file.close();
  }
}

// Copy Constructor:
GameState::GameState(const GameState& other)
    : tanks(other.tanks),
      bullets(other.bullets),
      map_size(other.map_size),
      turn(other.turn),
      mode(other.mode),
      initial_hp(other.initial_hp),
      log_filename(other.log_filename) {}

// Assignment operator:
GameState& GameState::operator=(const GameState& other) {
  if (this != &other) {
    tanks = other.tanks;
    bullets = other.bullets;
    map_size = other.map_size;
    turn = other.turn;
    mode = other.mode;
    initial_hp = other.initial_hp;
    log_filename = other.log_filename;
  }
  return *this;
}

void GameState::initialize_tanks(int tankA_x, int tankA_y, Direction tankA_dir,
                                 int tankB_x, int tankB_y,
                                 Direction tankB_dir) {
  tanks.clear();
  tanks.emplace_back(0, tankA_x, tankA_y, initial_hp, tankA_dir);  // Tank A
  tanks.emplace_back(1, tankB_x, tankB_y, initial_hp, tankB_dir);  // Tank B
}

void GameState::update_turn(MoveCommand moveA, MoveCommand moveB) {
  turn++;  // Add 1 to the turn number
  // ----------- OLD CODE ------------------//
  // ADD: logging
  // if (log_file.is_open()) {
  //   log_file << "Turn: " << turn << ":" << std::endl;
  // }
  // ------------- END OF OLD CODE ------------------//
  // Move tanks:
  tanks[0].move(moveA);  // Move tank A
  tanks[1].move(moveB);  // Move tank B
  // Update shoot timers:
  tanks[0].update_timer();
  tanks[1].update_timer();
  // Shoot bullets if the timer is 0:
  tanks[0].shoot(bullets);
  tanks[1].shoot(bullets);
  // MOVE BULLETS:
  for (auto& bullet : bullets) {
    bullet.move();
  }
  // handle_crash(); // Check whether tanks crash and handle accordingly
  // ----------------------------------------//
  // NEW: use handle_collision in gamelogic.h
  GameLogic::handle_collision(
      *this);  // Check whether tanks are hit by bullets and handle accordingly
  GameLogic::handle_tank_collision(
      *this);  // Check whether tanks crash and handle accordingly

  GameLogic::remove_out_of_bounds_bullets(*this);

  GameLogic::update_map_size(*this);  // Shrink the map size if necessary

  GameLogic::apply_boundary_damage(*this);
  //-----------------------------------------//
  // ----------- OLD CODE ------------------//
  // tankA.shoot(bullets);
  // tankB.shoot(bullets);
  //     for (auto& bullet : bullets) bullet.move(); // Move all bullets
  //     handle_collision(); //Check whether the two tanks crushed and handle
  //     accordingly check_out_of_map;   //Check if the tanks are out of the map
  //     and deduct 1 hp if they are if (turn % 16 == 0) shrink_map();   //
  //     Shrink the map size by 2 meters each 16 turns
  //  ------------------------------------------//
}
// ------------- OLD CODE ------------------//
// void GameState::shrink_map() {
//     // Shrink map szie by 2
//     // If the map size is already 2, it won't be shrinked anymore
//     map_size -= 2;
//     if (map_size < 2) map_size = 2;
// }
//
// void GameState::check_out_of_map() {
//     // Check whether the coordinate of tank A and B is out of the current map
//     // If any of them is out of the map, its hp will be deducted by 1
//     if (tankA.get_x() < 11 - map_size/2 || tankA.get_x() > 10 + map_size/2
//     ||tankA.get_y() < 11 - map_size/2 || tankA.get_y() > 10 + map_size/2){
//         tankA.deduct_hp(1);
//     }
//     if (tankB.get_x() < 11 - map_size/2 || tankB.get_x() > 10 + map_size/2
//     ||tankB.get_y() < 11 - map_size/2 || tankB.get_y() > 10 + map_size/2){
//         tankB.deduct_hp(1);
//     }
// }

// void GameState::handle_crash() {
//     // Use the coordinate to check if tank A and B are at the some position
//     // If they are, the hp of the tank with lower hp will be deducted to 0
//     // If they crash with the same hp, both of their hp will be deducted to 0
//         if (tankA.get_x() == tankB.get_x() && tankA.get_y() == tankB.get_y())
//         { if (tankA.get_hp() > tankB.get_hp())
//         tankB.deduct_hp(tankB.get_hp()); else if (tankA.get_hp() <
//         tankB.get_hp()) tankA.deduct_hp(tankA.get_hp()); else {
//             tankA.deduct_hp(tankA.get_hp());
//             tankB.deduct_hp(tankB.get_hp());
//         }
//     }
// }

// void GameState::handle_collision() {
//     // Check all bullets one by one to see whether they collides with a tank
//     // Once a collision happens, this bullet will be deleted from the vector
//     // and this tank's hp will be deducted by 1
//     for (auto it = bullets.begin(); it != bullets.end(); ) {
//         bool hit = false;
//         if (tankA.is_hit(*it)) {
//             tankA.deduct_hp(2);
//             hit = true;
//         }
//         else if (tankB.is_hit(*it)) {
//             tankB.deduct_hp(2);
//             hit = false;
//         }
//         if (hit) it = bullets.erase(it);
//         else it++;
//     }
// }
// ------------- END OF OLD CODE ------------------//

int GameState::check_win() const {  // add 'const' declaration
  bool tankA_alive = tanks[0].is_alive();
  bool tankB_alive = tanks[1].is_alive();
  if (!tankA_alive && !tankB_alive) {
    return 2;  // If both tanks are dead, return 2 to represent a draw
  }
  if (!tankA_alive) {
    return 1;  // If tank A is dead, return 1 to represent tank B wins
  }
  if (!tankB_alive) {
    return 0;  // If tank B is dead, return 0 to represent tank A wins
  }
  return -1;  // If both tanks are still alive, return -1 to represent that the
              // game continues

  // if (tankA.get_hp() <= 0 && tankB.get_hp() <= 0) return 0;   // If both the
  // hp of the tanks are 0, return 0 to represent the draw if (tankA.get_hp() <=
  // 0) return 2;  // If only tank A's hp is 0, return 2 to represent tank B
  // wins if (tankB.get_hp() <= 0) return 1;  // If only tank B's hp is 0,
  // return 1 to represent tank A wins else return -1; // If both tanks are
  // still alive, return -1 to represent that the game continues
}

bool GameState::is_game_over() const {
  return check_win() != -1;  // If the game is over, return true
}

void GameState::log_state() {
  // Log the current game state each turn
  if (!log_file.is_open()) {
    return;
  }
  log_file << "Turn " << turn << " :" << std::endl;

  log_file << "Tank A: (" << tanks[0].get_x() << ", " << tanks[0].get_y()
           << "), HP: " << tanks[0].get_hp() << ", Direction: ";
  switch (tanks[0].get_direction()) {
    case Direction::Up:
      log_file << "Up";
      break;
    case Direction::Down:
      log_file << "Down";
      break;
    case Direction::Left:
      log_file << "Left";
      break;
    case Direction::Right:
      log_file << "Right";
      break;
  }
  log_file << std::endl;

  log_file << "Tank B: (" << tanks[1].get_x() << ", " << tanks[1].get_y()
           << "), HP: " << tanks[1].get_hp() << ", Direction: ";
  switch (tanks[1].get_direction()) {
    case Direction::Up:
      log_file << "Up";
      break;
    case Direction::Down:
      log_file << "Down";
      break;
    case Direction::Left:
      log_file << "Left";
      break;
    case Direction::Right:
      log_file << "Right";
      break;
  }
  log_file << std::endl;
  log_file << "Bullets: " << bullets.size() << std::endl;
  log_file << "Map Size: " << map_size << std::endl;
  log_file << std::endl;
}

void GameState::log_end() {
  // Log the game result when game ends
  if (!log_file.is_open()) {
    return;
  }
  if (check_win() == 0)
    log_file << "Game ends! Tank A is the winner." << std::endl;
  else if (check_win() == 1)
    log_file << "Game ends! Tank B is the winner." << std::endl;
  else if (check_win() == 2)
    log_file << "Game ends! It's a draw competition." << std::endl;
}