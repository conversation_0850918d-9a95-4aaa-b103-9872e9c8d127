#!/bin/bash
#
# Commit message hook that validates commit message format
# Follows Conventional Commits specification: https://www.conventionalcommits.org/
#

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Read the commit message
COMMIT_MSG_FILE=$1
COMMIT_MSG=$(cat "$COMMIT_MSG_FILE")

# Skip validation for merge commits
if echo "$COMMIT_MSG" | grep -q "^Merge"; then
    exit 0
fi

# Skip validation for revert commits
if echo "$COMMIT_MSG" | grep -q "^Revert"; then
    exit 0
fi

echo -e "${GREEN}Validating commit message...${NC}"

# Define the regex pattern for conventional commits
# Format: type(scope): description
# Examples: 
#   feat: add new feature
#   fix(parser): resolve parsing issue
#   docs: update README
PATTERN="^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\(.+\))?: .{1,50}"

# Check if commit message matches the pattern
if ! echo "$COMMIT_MSG" | grep -qE "$PATTERN"; then
    echo -e "${RED}❌ Invalid commit message format!${NC}"
    echo ""
    echo -e "${YELLOW}Your commit message:${NC}"
    echo "  $COMMIT_MSG"
    echo ""
    echo -e "${YELLOW}Expected format:${NC}"
    echo "  type(scope): description"
    echo ""
    echo -e "${YELLOW}Valid types:${NC}"
    echo "  feat     - A new feature"
    echo "  fix      - A bug fix"
    echo "  docs     - Documentation only changes"
    echo "  style    - Changes that do not affect the meaning of the code"
    echo "  refactor - A code change that neither fixes a bug nor adds a feature"
    echo "  test     - Adding missing tests or correcting existing tests"
    echo "  chore    - Changes to the build process or auxiliary tools"
    echo "  perf     - A code change that improves performance"
    echo "  ci       - Changes to CI configuration files and scripts"
    echo "  build    - Changes that affect the build system or external dependencies"
    echo "  revert   - Reverts a previous commit"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  feat: add user authentication"
    echo "  fix(parser): handle edge case in input validation"
    echo "  docs: update installation instructions"
    echo "  test: add unit tests for calculator module"
    echo ""
    echo -e "${YELLOW}Scope is optional but recommended for larger projects.${NC}"
    echo ""
    exit 1
fi

# Check message length (first line should be <= 50 characters)
FIRST_LINE=$(echo "$COMMIT_MSG" | head -n1)
if [ ${#FIRST_LINE} -gt 50 ]; then
    echo -e "${YELLOW}⚠️  Warning: Commit message is longer than 50 characters (${#FIRST_LINE} chars)${NC}"
    echo "Consider making it more concise."
    echo ""
fi

# Check if description starts with lowercase (after type and colon)
DESCRIPTION=$(echo "$FIRST_LINE" | sed -E 's/^[a-z]+(\([^)]+\))?: //')
if echo "$DESCRIPTION" | grep -qE '^[A-Z]'; then
    echo -e "${YELLOW}⚠️  Warning: Description should start with lowercase letter${NC}"
    echo "Current: $DESCRIPTION"
    echo ""
fi

# Check if description ends with period
if echo "$FIRST_LINE" | grep -qE '\.$'; then
    echo -e "${YELLOW}⚠️  Warning: Description should not end with a period${NC}"
    echo ""
fi

echo -e "${GREEN}✅ Commit message format is valid!${NC}"
exit 0
