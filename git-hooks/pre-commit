#!/bin/bash
#
# Pre-commit hook that automatically formats C/C++ code using clang-format
# and runs clang-tidy checks before allowing the commit.
#

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Running pre-commit checks...${NC}"

# Check if clang-format is installed
if ! command -v clang-format &> /dev/null; then
    echo -e "${RED}Error: clang-format is not installed!${NC}"
    echo "Please install clang-format: sudo apt-get install clang-format"
    exit 1
fi

# Check if clang-tidy is installed
if ! command -v clang-tidy &> /dev/null; then
    echo -e "${YELLOW}Warning: clang-tidy is not installed. Skipping static analysis.${NC}"
    echo "Install with: sudo apt-get install clang-tidy"
    SKIP_TIDY=1
fi

# Get list of staged C/C++ files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(c|cpp|cc|cxx|h|hpp|hxx)$')

if [ -z "$STAGED_FILES" ]; then
    echo -e "${GREEN}No C/C++ files to check.${NC}"
    exit 0
fi

echo -e "${GREEN}Found C/C++ files to check:${NC}"
echo "$STAGED_FILES"

# Format files with clang-format
echo -e "\n${GREEN}Running clang-format...${NC}"
FORMAT_ISSUES=0

for FILE in $STAGED_FILES; do
    if [ -f "$FILE" ]; then
        # Check if file needs formatting
        if ! clang-format --dry-run --Werror "$FILE" &> /dev/null; then
            echo -e "${YELLOW}Formatting $FILE...${NC}"
            # Format the file
            clang-format -i "$FILE"
            # Re-stage the formatted file
            git add "$FILE"
            FORMAT_ISSUES=1
        fi
    fi
done

if [ $FORMAT_ISSUES -eq 1 ]; then
    echo -e "${GREEN}Files have been formatted and re-staged.${NC}"
fi

# Run clang-tidy if available
if [ -z "$SKIP_TIDY" ]; then
    echo -e "\n${GREEN}Running clang-tidy...${NC}"
    TIDY_ISSUES=0
    
    for FILE in $STAGED_FILES; do
        if [ -f "$FILE" ]; then
            echo "Checking $FILE..."
            if ! clang-tidy "$FILE" -- -std=c++17 2>/dev/null; then
                echo -e "${YELLOW}clang-tidy found issues in $FILE${NC}"
                TIDY_ISSUES=1
            fi
        fi
    done
    
    if [ $TIDY_ISSUES -eq 1 ]; then
        echo -e "${YELLOW}clang-tidy found some issues. Please review them.${NC}"
        echo -e "${YELLOW}You can still commit, but consider fixing the issues.${NC}"
    fi
fi

echo -e "\n${GREEN}Pre-commit checks completed!${NC}"
exit 0
