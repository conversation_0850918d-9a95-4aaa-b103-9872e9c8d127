#!/bin/bash
#
# Git Hooks Installation Script
# This script installs the shared git hooks for the project
#

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Installing Git hooks for VG101 P2 Team 05...${NC}"

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo -e "${RED}Error: Not in a git repository!${NC}"
    echo "Please run this script from the project root directory."
    exit 1
fi

# Check if git-hooks directory exists
if [ ! -d "git-hooks" ]; then
    echo -e "${RED}Error: git-hooks directory not found!${NC}"
    echo "Please make sure you're in the correct project directory."
    exit 1
fi

# Check if hook files exist
if [ ! -f "git-hooks/pre-commit" ] || [ ! -f "git-hooks/commit-msg" ]; then
    echo -e "${RED}Error: Hook files not found in git-hooks directory!${NC}"
    exit 1
fi

# Install the hooks
echo -e "${YELLOW}Installing pre-commit hook...${NC}"
cp git-hooks/pre-commit .git/hooks/
chmod +x .git/hooks/pre-commit

echo -e "${YELLOW}Installing commit-msg hook...${NC}"
cp git-hooks/commit-msg .git/hooks/
chmod +x .git/hooks/commit-msg

# Verify installation
if [ -x ".git/hooks/pre-commit" ] && [ -x ".git/hooks/commit-msg" ]; then
    echo -e "${GREEN}✓ Git hooks installed successfully!${NC}"
    echo ""
    echo -e "${GREEN}The following hooks are now active:${NC}"
    echo "  - pre-commit: Automatically formats C++ code with clang-format"
    echo "  - commit-msg: Validates commit message format"
    echo ""
    echo -e "${YELLOW}Note: Make sure you have clang-format and clang-tidy installed:${NC}"
    echo "  sudo apt-get install clang-format clang-tidy"
    echo ""
    echo -e "${GREEN}Happy coding!${NC}"
else
    echo -e "${RED}Error: Failed to install hooks properly!${NC}"
    exit 1
fi
